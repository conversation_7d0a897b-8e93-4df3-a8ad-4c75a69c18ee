{"name": "react-native-gifted-charts", "version": "1.4.63", "description": "The most complete library for Bar, Line, Area, Pie, Donut, Stacked Bar, Population Pyramid and Radar charts in React Native. Allows 2D, 3D, gradient, animations and live data updates.", "main": "dist/index.js", "files": ["dist"], "scripts": {"android": "react-native run-android", "build": "rm -rf ./dist && tsc -p . && ./build.sh", "prepare": "npm run build", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "repository": "https://github.com/Ab<PERSON>anda<PERSON>-<PERSON><PERSON><PERSON><PERSON>/react-native-gifted-charts", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/A<PERSON><PERSON>anda<PERSON>-<PERSON>)", "license": "MIT", "bugs": {"url": "https://github.com/Ab<PERSON>anda<PERSON>-<PERSON><PERSON><PERSON><PERSON>/react-native-gifted-charts/issues"}, "homepage": "https://gifted-charts.web.app/", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "dependencies": {"gifted-charts-core": "0.1.65"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.24.9", "@babel/runtime": "^7.25.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native/metro-config": "^0.74.85", "@types/jest": "^29.5.12", "@types/node": "^22.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.77.0", "react": "^18.2.0", "react-native": "^0.74.3", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^15.4.0", "react-test-renderer": "18.2.0", "ts-jest": "^29.2.3", "typescript": "^5.5.4"}, "peerDependencies": {"expo-linear-gradient": "*", "react": "*", "react-native": "*", "react-native-linear-gradient": "*", "react-native-svg": "*"}, "peerDependenciesMeta": {"react-native-linear-gradient": {"optional": true}, "expo-linear-gradient": {"optional": true}}, "keywords": ["chart", "charts", "graph", "data visualization", "bar", "pie", "donut", "area", "line", "radar", "star", "population", "pyramid", "react", "react native", "react-native"]}